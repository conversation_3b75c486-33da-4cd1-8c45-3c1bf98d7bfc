import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

export default function ClientAcquisition() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    leadGeneration: false,
    salesFunnel: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  // Track which questionnaires have actual responses saved
  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    leadGeneration: false,
    salesFunnel: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  // Loading state for initial data fetch
  const [loading, setLoading] = useState(true);
  
  // Handle questionnaire submission
  const handleSubmit = (data) => {
    console.log('Questionnaire submitted:', data);
    setSubmissionResult({
      success: true,
      message: 'Thank you for your submission!',
      data: data
    });
    setShowQuestionnaire(false);
  };
  
  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);
    
    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your client acquisition strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
      setShowQuestionnaire(false);
    }, 2000);
  };
  // Context prompt specific to client acquisition
  const contextPrompt = `You are an expert in client acquisition strategies and techniques. 
  Provide detailed, actionable advice with examples when possible. 
  Focus on practical methods for identifying, attracting, and converting potential clients. 
  Include industry best practices, data-driven approaches, and innovative strategies 
  that businesses can implement to improve their client acquisition efforts.`;

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
          <h2 className="raleway-title-h2 mb-4">Client Acquisition Strategies</h2>
          <p className="body-text mb-4">
            Comprehensive tools and methodologies to attract, engage, and convert potential clients for your business.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-amber-50 hover:bg-amber-100">
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Lead Generation</h3>
              <p className="body-text">Develop effective strategies to generate quality leads.</p>
            </div>
            <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-amber-50 hover:bg-amber-100">
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Sales Funnel</h3>
              <p className="body-text">Create and optimize your sales funnel for better conversion rates.</p>
            </div>
            <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-amber-50 hover:bg-amber-100">
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Marketing Campaigns</h3>
              <p className="body-text">Design targeted marketing campaigns to attract potential clients.</p>
            </div>
            <div className="p-4 border rounded-lg shadow-sm hover:shadow-md transition-shadow bg-amber-50 hover:bg-amber-100">
              <h3 className="raleway-title-h3 mb-2 text-amber-800">Client Onboarding</h3>
              <p className="body-text">Develop a smooth onboarding process for new clients.</p>
            </div>
          </div>
        </div>
        
        {/* Questionnaire Section with Toggle */}
        <div className="text-center mb-8 p-4 bg-blue-50 rounded-lg border border-blue-100 shadow-md" style={{ borderWidth: '0.5px' }}>
          <h3 className="raleway-title-h3 mb-2">Client Acquisition Questionnaire</h3>
          <p className="body-text mb-4">Complete our comprehensive questionnaire to receive a customized client acquisition strategy for your business.</p>
          <button
          className="mb-4 px-6 py-2 bg-white hover:bg-gray-50 text-gray-800 rounded-lg border border-amber-200 shadow-sm transition-colors uppercase"
          onClick={() => setShowQuestionnaire(!showQuestionnaire)}
        >
          {showQuestionnaire ? 'HIDE QUESTIONNAIRE' : 'START QUESTIONNAIRE'}
        </button>
          
          {showQuestionnaire && (
            <div className="mt-6 p-4 rounded-lg">
              <p className="body-text mb-4 text-center">
                Please complete this questionnaire to receive a personalized client acquisition strategy designed specifically for your business type, target audience, and growth objectives.
              </p>
              <QuestionnaireLoader 
                title="Client Acquisition Questionnaire" 
                description="Please complete this questionnaire to help us understand your client acquisition needs."
                specificQuestionnaires={[
                  'lead-generation_strategy-quetstionnaire-01.yaml',
                  '03-ideal-customer-profile.yaml',
                  '04-customer-acquisition-strategy.yaml'
                ]}
                defaultQuestionnaire="04-customer-acquisition-strategy.yaml"
                onSubmit={handleSubmit}
                onGenerateStrategy={handleGenerateStrategy}
                showLocalSave={true}
              />
              <div className="mt-4 text-center">
                <button 
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition"
                  onClick={() => setShowQuestionnaire(false)}
                >
                  Cancel
                </button>
                <button 
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition ml-4"
                  onClick={() => handleGenerateStrategy()}
                >
                  Complete Questionnaire
                </button>
              </div>
            </div>
          )}
        </div>
      
      {/* Questionnaire Success Message */}
      {submissionResult && (
        <div className="bg-green-50 p-6 rounded-lg shadow-md border border-green-200 mb-8">
          <h2 className="text-xl font-semibold mb-4 text-green-700">
            <svg className="inline-block w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {submissionResult.message}
          </h2>
          {submissionResult.isStrategy ? (
            <div>
              <p className="mb-4">
                Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                we've generated a customized client acquisition strategy for your business.
              </p>
              <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                <h3 className="raleway-title-h3 mb-2">Your Client Acquisition Strategy</h3>
                <p className="body-text mb-2">This strategy is tailored to help you attract and convert new clients:</p>
                <ul className="list-disc pl-5 space-y-2 body-text">
                  <li>Develop targeted social media campaigns focused on your key audience demographics</li>
                  <li>Implement a content marketing strategy with blog posts and case studies</li>
                  <li>Create a referral program with incentives for existing clients</li>
                  <li>Optimize your website for lead generation with clear CTAs and landing pages</li>
                  <li>Establish strategic partnerships with complementary businesses</li>
                </ul>
              </div>
            </div>
          ) : (
            <p className="mb-4">
              We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
              Our team will analyze your information and provide tailored client acquisition recommendations.
            </p>
          )}
          <button 
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
            onClick={() => setSubmissionResult(null)}
          >
            Close
          </button>
        </div>
      )}
      
      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized client acquisition strategy...</p>
          </div>
        </div>
      )}
      
      {/* Questionnaire Loader Component */}
      {showQuestionnaire && (
        <div className="mb-8 p-6 bg-beige rounded-lg shadow-md">
          <p className="body-text mb-4 text-center italic">
            Please complete this questionnaire to receive a personalized client acquisition strategy designed specifically for your business type, target audience, and growth objectives.
          </p>
          <div className="text-center mb-4">
            <button 
              className="questionnaire-btn"
              onClick={() => {
                const questionsElement = document.querySelector('.mb-8.p-6.bg-beige');
                if (questionsElement) {
                  questionsElement.scrollIntoView({ behavior: 'smooth' });
                }
              }}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path>
              </svg>
              Complete Client Acquisition Questionnaire
            </button>
          </div>
          <QuestionnaireLoader 
            title="Client Acquisition Questionnaire" 
            description="Please complete this questionnaire to help us understand your client acquisition needs."
            specificQuestionnaires={[
              'lead-generation_strategy-quetstionnaire-01.yaml',
              '03-ideal-customer-profile.yaml',
              '04-customer-acquisition-strategy.yaml'
            ]}
            defaultQuestionnaire="04-customer-acquisition-strategy.yaml"
            onSubmit={handleSubmit}
            onGenerateStrategy={handleGenerateStrategy}
            showLocalSave={true}
          />
        </div>
      )}
      
      {/* AI Agent Component */}
      {!showQuestionnaire && (
        <AIAgent 
          title="Client Acquisition Assistant" 
          description="Ask questions about client acquisition strategies, techniques, and best practices." 
          contextPrompt={contextPrompt} 
        />
      )}
      </div>
    </div>
  );
}
