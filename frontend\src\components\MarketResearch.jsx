import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';
import './MarketResearch.css';

function MarketResearch() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null); // null | 'default' | 'consumerInsight' | 'segmentation' | 'trends' | 'focusGroups'
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();
  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    consumerInsight: false,
    segmentation: false,
    trends: false,
    focusGroups: false
  });
  
  // Track which questionnaires have actual responses saved
  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    consumerInsight: false,
    segmentation: false,
    trends: false,
    focusGroups: false
  });
  
  // Loading state for initial data fetch
  const [loading, setLoading] = useState(true);
  
  // Load completed questionnaires from Supabase on component mount
  useEffect(() => {
    const loadCompletedQuestionnaires = async () => {
      try {
        setLoading(true);
        // Only load data if user is authenticated
        if (user) {
          const { completionStatus, error } = await getAllCompletedQuestionnaires(true); // true = user-specific data
          
          if (error) {
            console.error('Error loading completed questionnaires:', error);
            fallbackToLocalStorage();
          } else if (completionStatus) {
            setCompletedQuestionnaires(completionStatus);
            setActualCompletedQuestionnaires(completionStatus); // In Supabase, completed = has responses
          }
        } else {
          // Not authenticated, use localStorage
          fallbackToLocalStorage();
        }
      } catch (err) {
        console.error('Error in loadCompletedQuestionnaires:', err);
        fallbackToLocalStorage();
      } finally {
        setLoading(false);
      }
    };
    
    const fallbackToLocalStorage = () => {
      // Fall back to localStorage
      const saved = localStorage.getItem('completedQuestionnaires');
      if (saved) {
        const localData = JSON.parse(saved);
        setCompletedQuestionnaires(localData);
        
        // Check localStorage for actual responses
        setActualCompletedQuestionnaires({
          consumerInsight: !!localStorage.getItem('questionnaire_responses_consumerInsight'),
          segmentation: !!localStorage.getItem('questionnaire_responses_segmentation'),
          trends: !!localStorage.getItem('questionnaire_responses_trends'),
          focusGroups: !!localStorage.getItem('questionnaire_responses_focusGroups')
        });
      }
    };
    
    loadCompletedQuestionnaires();
  }, [user]); // Re-run when user changes
  
  // Questionnaire configurations
  const questionnaireConfigs = {
    default: {
      title: "Market Research Questionnaire",
      description: "Please complete this questionnaire to help us understand your market research needs.",
      files: [
        '00-strategy-questionnaire.yaml',
        '03-ideal-customer-profile.yaml',
        'lead-generation_strategy-quetstionnaire-01.yaml'
      ],
      defaultFile: '00-strategy-questionnaire.yaml'
    },
    consumerInsight: {
      title: "Consumer Preferences & Behavior Data Collection Questionnaire",
      description: "This questionnaire is designed to help gather and analyze data about consumer preferences and behaviors specifically for spiritual fine jewelry.",
      files: ['consumer-insight-fixed.yaml'],
      defaultFile: 'consumer-insight-fixed.yaml'
    },
    segmentation: {
      title: "Market Segmentation",
      description: "Divide your market into distinct groups with similar needs or characteristics.",
      files: ['market-segmentation-fixed.yaml'],
      defaultFile: 'market-segmentation-fixed.yaml'
    },
    trends: {
      title: "Trend Analysis",
      description: "Identify and analyze emerging trends in your industry or market.",
      files: ['trend-analysis-questionnaire.yaml'],
      defaultFile: 'trend-analysis-questionnaire.yaml'
    },
    focusGroups: {
      title: "Focus Groups",
      description: "Conduct qualitative research through moderated group discussions.",
      files: ['focus-groups-questionnaire.yaml'],
      defaultFile: 'focus-groups-questionnaire.yaml'
    }
  };
  
  // Handle questionnaire submission
  const handleSubmit = async (data) => {
    console.log('Questionnaire submitted:', data);
    
    // Mark the current questionnaire as completed
    if (activeQuestionnaire) {
      const updatedCompletedQuestionnaires = {
        ...completedQuestionnaires,
        [activeQuestionnaire]: true
      };
      
      // Save to state and localStorage as backup
      setCompletedQuestionnaires(updatedCompletedQuestionnaires);
      localStorage.setItem('completedQuestionnaires', JSON.stringify(updatedCompletedQuestionnaires));
      
      // Get questionnaire name for display
      const questionnaireName = getQuestionnaireNameFromKey(activeQuestionnaire);
      
      // Prepare response data
      const responseData = {
        questionnaire: questionnaireName,
        responses: data,
        timestamp: new Date().toISOString(),
        user_id: user?.id || null // Add user ID if authenticated
      };
      
      console.log('Saving questionnaire responses:', responseData);
      
      // Save to Supabase if user is authenticated, otherwise use localStorage
      if (user) {
        try {
          const { error } = await saveQuestionnaireResponses(
            activeQuestionnaire,
            questionnaireName,
            data
          );
          
          if (error) {
            console.error('Error saving to Supabase:', error);
            // Fall back to localStorage if Supabase fails
            localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
          }
          
          // Update the actualCompletedQuestionnaires state
          setActualCompletedQuestionnaires(prev => ({
            ...prev,
            [activeQuestionnaire]: true
          }));
          
          // Store in sessionStorage for immediate viewing
          sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
        } catch (err) {
          console.error('Error in handleSubmit:', err);
          // Fall back to localStorage
          localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
        }
      } else {
        // Not authenticated, use localStorage only
        localStorage.setItem(`questionnaire_responses_${activeQuestionnaire}`, JSON.stringify(responseData));
        
        // Update the actualCompletedQuestionnaires state
        setActualCompletedQuestionnaires(prev => ({
          ...prev,
          [activeQuestionnaire]: true
        }));
        
        // Store in sessionStorage for immediate viewing
        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(responseData));
      }
    }
    
    // Show submission result popup
    setSubmissionResult({
      success: true,
      message: 'Questionnaire submitted successfully!',
      data: data
    });
    
    // Close the questionnaire immediately
    setActiveQuestionnaire(null);
    
    // Close the popup after 3 seconds
    setTimeout(() => {
      setSubmissionResult(null);
    }, 3000);
  };
  
  // Helper function to get readable questionnaire name from key
  const getQuestionnaireNameFromKey = (key) => {
    switch(key) {
      case 'consumerInsight': return 'Consumer Insights';
      case 'segmentation': return 'Market Segmentation';
      case 'trends': return 'Trend Analysis';
      case 'focusGroups': return 'Focus Groups';
      default: return 'Questionnaire';
    }
  };
  
  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);
    
    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your market research strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
      setActiveQuestionnaire(null);
    }, 2000);
  };
  
  // Toggle questionnaire visibility
  const toggleQuestionnaire = (questionnaireType) => {
    console.log('Toggling questionnaire:', questionnaireType, 'Current active:', activeQuestionnaire);
    setActiveQuestionnaire(activeQuestionnaire === questionnaireType ? null : questionnaireType);
  };
  
  const contextPrompt = `You are a market research expert. Your task is to provide detailed, accurate, and helpful information about market research topics, methodologies, and best practices. Focus on providing actionable insights that can help businesses understand their target markets, customer needs, and industry trends.`;
  
  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Market Research</h2>
        <p className="body-text mb-4">
          Comprehensive tools and methodologies to understand your target market and customer needs.
        </p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'consumerInsight' ? 'ring-2 ring-green-400 bg-green-100' : 'bg-green-50 hover:bg-green-100'}`}
            onClick={() => toggleQuestionnaire('consumerInsight')}
          >
            <h3 className="raleway-title-h3 mb-2 text-green-800">Consumer Insights</h3>
            <p className="body-text">Gather and analyze data about consumer preferences and behaviors.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'segmentation' ? 'ring-2 ring-green-400 bg-green-100' : 'bg-green-50 hover:bg-green-100'}`}
            onClick={() => toggleQuestionnaire('segmentation')}
          >
            <h3 className="raleway-title-h3 mb-2 text-green-800">Market Segmentation</h3>
            <p className="body-text">Divide your market into distinct groups with similar needs or characteristics.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'trends' ? 'ring-2 ring-green-400 bg-green-100' : 'bg-green-50 hover:bg-green-100'}`}
            onClick={() => toggleQuestionnaire('trends')}
          >
            <h3 className="raleway-title-h3 mb-2 text-green-800">Trend Analysis</h3>
            <p className="body-text">Identify and analyze emerging trends in your industry or market.</p>
          </div>
          <div
            className={`p-4 border rounded-lg shadow-sm hover:shadow-md transition-all cursor-pointer ${activeQuestionnaire === 'focusGroups' ? 'ring-2 ring-green-400 bg-green-100' : 'bg-green-50 hover:bg-green-100'}`}
            onClick={() => toggleQuestionnaire('focusGroups')}
          >
            <h3 className="raleway-title-h3 mb-2 text-green-800">Focus Groups</h3>
            <p className="body-text">Conduct qualitative research through moderated group discussions.</p>
          </div>
        </div>
        
        {/* Questionnaire Completion Status */}
        <div className="mt-8 border-t pt-4">
          <h4 className="raleway-title-h4 mb-3">Questionnaire Completion Status:</h4>
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.consumerInsight ? (
                    <div className="completion-badge bg-green-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-green-800">Consumer Insights</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.consumerInsight ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.consumerInsight && (
                <button
                  className="px-3 py-1 bg-green-600 text-white text-sm rounded hover:bg-green-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      // First try to get responses from Supabase
                      const { data, error } = await getQuestionnaireResponses('consumerInsight');
                      
                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        // Fall back to localStorage
                        const storedData = localStorage.getItem('questionnaire_responses_consumerInsight');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        // Format the data for ResponseView component
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.consumerInsight && !actualCompletedQuestionnaires.consumerInsight && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Consumer Insights questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.segmentation ? (
                    <div className="completion-badge bg-blue-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-blue-800">Market Segmentation</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.segmentation ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.segmentation && (
                <button
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      // First try to get responses from Supabase
                      const { data, error } = await getQuestionnaireResponses('segmentation');
                      
                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        // Fall back to localStorage
                        const storedData = localStorage.getItem('questionnaire_responses_segmentation');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        // Format the data for ResponseView component
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.segmentation && !actualCompletedQuestionnaires.segmentation && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Market Segmentation questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.trends ? (
                    <div className="completion-badge bg-purple-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-purple-800">Trend Analysis</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.trends ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.trends && (
                <button
                  className="px-3 py-1 bg-purple-600 text-white text-sm rounded hover:bg-purple-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      // First try to get responses from Supabase
                      const { data, error } = await getQuestionnaireResponses('trends');
                      
                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        // Fall back to localStorage
                        const storedData = localStorage.getItem('questionnaire_responses_trends');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        // Format the data for ResponseView component
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.trends && !actualCompletedQuestionnaires.trends && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Trend Analysis questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>
            
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center">
                <div className="completion-indicator">
                  {completedQuestionnaires.focusGroups ? (
                    <div className="completion-badge bg-yellow-500">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  ) : (
                    <div className="completion-badge bg-gray-200">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                  )}
                </div>
                <div className="ml-3">
                  <h5 className="raleway-title-h5 text-yellow-800">Focus Groups</h5>
                  <p className="text-sm text-gray-600">{completedQuestionnaires.focusGroups ? "Completed" : "Not completed"}</p>
                </div>
              </div>
              {actualCompletedQuestionnaires.focusGroups && (
                <button
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700 transition flex items-center"
                  onClick={async () => {
                    try {
                      // First try to get responses from Supabase
                      const { data, error } = await getQuestionnaireResponses('focusGroups');
                      
                      if (error || !data) {
                        console.error('Error fetching from Supabase:', error);
                        // Fall back to localStorage
                        const storedData = localStorage.getItem('questionnaire_responses_focusGroups');
                        if (storedData) {
                          sessionStorage.setItem('questionnaire_view_responses', storedData);
                          navigate('/responses');
                        } else {
                          throw new Error('No data found in localStorage either');
                        }
                      } else {
                        // Format the data for ResponseView component
                        const formattedData = {
                          questionnaire: data.questionnaire_name,
                          responses: data.responses,
                          timestamp: data.created_at
                        };
                        sessionStorage.setItem('questionnaire_view_responses', JSON.stringify(formattedData));
                        navigate('/responses');
                      }
                    } catch (err) {
                      console.error('Error viewing responses:', err);
                      setSubmissionResult({
                        success: false,
                        message: 'Error loading responses. Please try again.',
                        data: null
                      });
                      setTimeout(() => {
                        setSubmissionResult(null);
                      }, 3000);
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Responses
                </button>
              )}
              {completedQuestionnaires.focusGroups && !actualCompletedQuestionnaires.focusGroups && (
                <button
                  className="px-3 py-1 bg-gray-400 text-white text-sm rounded hover:bg-gray-500 transition flex items-center"
                  onClick={() => {
                    setSubmissionResult({
                      success: false,
                      message: 'No responses available. Please complete the Focus Groups questionnaire first.',
                      data: null
                    });
                    setTimeout(() => {
                      setSubmissionResult(null);
                    }, 3000);
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  No Responses
                </button>
              )}
            </div>
          </div>
          
          {/* Removed button from here */}

        </div>
        
        {/* Strategy Generation Section */}
        <div className="mt-8 p-6 bg-white rounded-lg border border-blue-200 shadow-md">
          <h4 className="raleway-title-h4 mb-3 text-blue-800">GENERATE MARKETING STRATEGY</h4>
          <p className="body-text mb-4">
            Ready to turn your questionnaire responses into an actionable marketing strategy?
            Click the button below to generate a comprehensive strategy tailored to your business needs.
          </p>
          <div className="flex flex-wrap gap-4">
            {/* Generate Strategy button */}
            <button
              className="px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center"
              onClick={() => {
                // Store the responses in sessionStorage for the strategy page
                const responseData = {
                  questionnaire: 'Market Research Questionnaires',
                  responses: completedQuestionnaires,
                  timestamp: new Date().toISOString()
                };
                sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
                // Navigate to the strategy page
                navigate('/strategy');
              }}
              disabled={!Object.values(completedQuestionnaires).some(value => value)}
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              Generate Strategy
            </button>
          </div>
        </div>
      </div>
      
      {/* Questionnaire Section */}
      {activeQuestionnaire && (
        <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
          <div className="flex justify-end mb-4">
            <button
              onClick={() => setActiveQuestionnaire(null)}
              className="text-gray-500 hover:text-gray-700"
              aria-label="Close questionnaire"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <QuestionnaireLoader
            key={activeQuestionnaire}
            title={questionnaireConfigs[activeQuestionnaire].title}
            description={questionnaireConfigs[activeQuestionnaire].description}
            specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
            defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
            onSubmit={handleSubmit}
            onGenerateStrategy={handleGenerateStrategy}
            showLocalSave={false}
            hideGenerateStrategyButton={true}
            hideQuestionnaireSelector={true}
          />
        </div>
      )}
      
      {/* Questionnaire Success Message Popup */}
      {submissionResult && (
        <div className="fixed inset-0 flex items-center justify-center z-50">
          <div className="absolute inset-0 bg-black opacity-30"></div>
          <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
            <div className="flex items-center justify-center mb-4 text-green-500">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
              {submissionResult.message}
            </h2>
            {submissionResult.isStrategy ? (
              <div>
                <p className="mb-4">
                  Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                  we've generated a customized market research strategy for your business.
                </p>
                <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                  <h3 className="raleway-title-h3 mb-2">Your Market Research Strategy</h3>
                  <p className="body-text mb-2">This strategy is tailored to your specific industry and target audience:</p>
                  <ul className="list-disc pl-5 space-y-2 body-text">
                    <li>Conduct targeted surveys with your identified customer segments to gather quantitative data</li>
                    <li>Organize focus groups with 6-8 participants from your target demographic</li>
                    <li>Perform competitor analysis focusing on the 3-5 main competitors in your market</li>
                    <li>Analyze industry trends and market size data specific to your sector</li>
                    <li>Develop customer personas based on demographic and psychographic research</li>
                  </ul>
                </div>
              </div>
            ) : (
              <p className="mb-4">
                We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
                Our team will analyze your information and provide tailored market research recommendations.
              </p>
            )}
            <button 
              className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition"
              onClick={() => setSubmissionResult(null)}
            >
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized market research strategy...</p>
          </div>
        </div>
      )}
      
      {/* Market Research AI Agent */}
      <AIAgent 
        title="Market Research Assistant" 
        description="Ask questions about market research methodologies, tools, and best practices."
        contextPrompt={contextPrompt}
      />
    </div>
  );
}

export default MarketResearch;
